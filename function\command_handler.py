import struct
import binascii
import time
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, List
import os
from datetime import datetime
import threading
import queue
try:
    import crcmod
except ImportError:
    print("Warning: crcmod library not found. Please install it using: pip install crcmod")
    crcmod = None

class CommandHandler:
    """指令处理类，负责生成指令和验证响应"""

    def __init__(self, logger_manager=None):
        self.logger_manager = logger_manager

        # 初始化CRC16函数 (CRC-16-X25, 多项式 0x1021)
        if crcmod:
            self.crc16_func = crcmod.mkCrcFun(
                poly = 0x11021,
                initCrc = 0x0000,
                rev = True,
                xorOut = 0xFFFF)
        else:
            self.crc16_func = None

        # 初始化电源指令处理器
        self.power_handler = PowerCommandHandler(logger_manager, self)
            
        # 指令状态管理
        self.command_state = {
            "current_command": None,
            "current_frame": 0,
            "total_frames": 0,
            "waiting_for_response_frame": False,
            "waiting_for_middle_frame": False,
            "send_response_frame_next": False,
            "middle_frame_count": 0,
            "max_middle_frame_attempts": 50,  # 最大中间帧尝试次数
            "command_params": {}
        }
        
        # 固定指令映射表
        self.fixed_commands = {
            "广播进入装备模式": bytes.fromhex("7E FF BB 90 06 00 48 57 03 01 01 01 9D 29 7E"),
            "广播恢复出厂序列号": bytes.fromhex("7E FF BB 90 05 00 48 57 03 02 00 A1 83 7E"),
            "默认序列号分配地址": bytes.fromhex("7E FF BF 81 F0 18 01 13 48 57 5F 4E 45 45 44 53 5F 41 4E 54 5F 43 4F 4E 46 49 47 02 01 01 F7 C7 7E"),
            "写温度信息": bytes.fromhex("7E 01 10 90 05 00 48 57 88 19 00 60 41 7E"),
            "回读温度信息": bytes.fromhex("7E 01 10 90 03 00 48 57 89 59 AA 7E"),
            "Flash MINI自检": bytes.fromhex("7E 01 10 90 04 00 48 57 56 04 90 B9 7E"),
            "查询软硬件版本信息": bytes.fromhex("7E 01 10 05 00 00 2F 3E 7E"),
            "设备1分配地址并建立连接": bytes.fromhex("7E FF BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 02 01 01 91 A8 7E"),
            "天线校准命令": bytes.fromhex("7E 01 10 31 00 00 E0 DB 7E"),
            "设备2分配地址并建立连接": bytes.fromhex("7E FF BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 32 02 01 01 5C 8D 7E"),
            "备份序列号": bytes.fromhex("7E 01 10 90 03 00 48 57 EA C4 FB 7E"),
            "回读RCU序列号": bytes.fromhex("7E 01 10 90 04 00 48 57 80 06 69 91 7E"),
            "回读备份序列号": bytes.fromhex("7E 01 10 90 03 00 48 57 EB 4D EA 7E"),
            "响应帧": bytes.fromhex("7E 01 93 8D B0 7E"),
            "中间帧": bytes.fromhex("7E 01 11 97 17 7E")
        }
        
        # 内部使用的指令（不对外暴露）
        self._internal_commands = {
            "查询软硬件版本信息_第二次": bytes.fromhex("7E 01 10 05 00 00 2F 3E 7E")
        }
        
        # 指令响应映射表
        self.expected_responses = {
            "广播进入装备模式": bytes.fromhex("7E 00 BB 90 04 00 48 57 03 00 78 C5 7E"),
            "广播恢复出厂序列号": bytes.fromhex("7E 00 BB 90 04 00 48 57 03 00 78 C5 7E"),
            "默认序列号分配地址": bytes.fromhex("7E 01 BF 81 F0 18 01 13 48 57 5F 4E 45 45 44 53 5F 41 4E 54 5F 43 4F 4E 46 49 47 04 01 01 F5 02 7E"),
            "设备1分配地址并建立连接": bytes.fromhex("7E 01 BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 04 01 01 93 6D 7E"),
            "设备2分配地址并建立连接": bytes.fromhex("7E 01 BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 32 04 01 01 5E 48 7E"),
            "写温度信息": bytes.fromhex("7E 01 30 90 04 00 48 57 88 00 6F 8C 7E"),
            "查询软硬件版本信息": None,  # 使用动态验证，不固定响应格式
            "天线校准命令": bytes.fromhex("7E 01 30 31 01 00 00 E0 2D 7E"),
            "写RCU序列号": bytes.fromhex("7E 01 30 90 04 00 48 57 81 00 77 5B 7E"),
            "备份序列号": bytes.fromhex("7E 01 30 90 04 00 48 57 EA 00 8A DA 7E"),
            "响应帧": bytes.fromhex("7E 01 73 83 57 7E"),
            "Flash MINI自检": bytes.fromhex("7E 01 30 90 04 00 48 57 56 00 44 49 7E"),
            "设备设置装备模式": bytes.fromhex("7E 01 30 90 04 00 48 57 50 00 94 1D 7E")
        }
        
        # 查询软硬件版本信息的帧指令映射
        self.version_query_frames = {
            0: bytes.fromhex("7E 01 10 05 00 00 2F 3E 7E"),  # 单帧指令
        }
        
        # 查询软硬件版本信息的多个响应模式
        self.version_query_responses = {
            # 单帧响应格式
            "frame_0": None,  # 使用动态验证，不固定响应格式
        }
        
        # 不需要发送响应帧的指令
        self.no_response_commands = {
            "广播进入装备模式", "广播恢复出厂序列号", "响应帧", "中间帧",
            # 电源指令集 - 都不需要发送响应帧
            "查询上位机状态", "切换供电开关", "查询供电开关", "读取电压值", "读取电流值"
        }
        
        # 特殊帧标识
        self.special_frame = bytes.fromhex("7E 01 31 95 36 7E")
        self.middle_frame = bytes.fromhex("7E 01 11 97 17 7E")
        self.response_frame = bytes.fromhex("7E 01 93 8D B0 7E")
        
    def _convert_file_type_to_hex(self, file_type: str) -> str:
        """将用户输入的文件类型转换为十六进制值"""
        # print(f"DEBUG - _convert_file_type_to_hex - 输入的文件类型: '{file_type}'")
        if file_type == "":
            # print("DEBUG - _convert_file_type_to_hex - 使用默认值: '05'")
            return "05"  # 默认使用模块信息文件
        elif file_type == "模块信息文件":
            # print("DEBUG - _convert_file_type_to_hex - 转换为: '05'")
            return "05"
        elif file_type == "天线信息文件":
            # print("DEBUG - _convert_file_type_to_hex - 转换为: '06'")
            return "06"
        elif file_type in ["05", "06"]:
            # 支持直接的十六进制值
            # print(f"DEBUG - _convert_file_type_to_hex - 直接使用十六进制值: '{file_type}'")
            return file_type
        else:
            error_msg = f"不支持的文件类型: {file_type}，支持的类型：''、'模块信息文件'、'天线信息文件'、'05'、'06'"
            # print(f"DEBUG - _convert_file_type_to_hex - 错误: {error_msg}")
            raise ValueError(error_msg)
    
    def generate_command(self, command_name: str, **kwargs) -> Optional[bytes]:
        """生成指令"""
        try:
            # 首先检查是否是电源指令
            power_commands = self.power_handler.get_power_command_list()
            if command_name in power_commands:
                return self.power_handler.generate_power_command(command_name, **kwargs)

            if command_name == "读取电机转速":
                # 添加调试日志
                content = kwargs.get('content', '')
                # print(f"DEBUG - generate_command - 读取电机转速 - content参数: '{content}'")
                param = 0x01 if content == '2' else 0x00
                # print(f"DEBUG - generate_command - 读取电机转速 - 生成的参数值: 0x{param:02X}")
                
                # 构建指令内容（不包括帧头和帧尾）
                cmd_data = bytearray()
                cmd_data.extend(bytes.fromhex("01 10 90 04 00 48 57 90"))
                cmd_data.append(param)
                
                # 计算CRC
                crc = self._calculate_crc(cmd_data)
                
                # 构建完整指令
                cmd = bytearray()
                cmd.append(0x7E)  # 帧头
                cmd.extend(cmd_data)
                cmd.extend(crc.to_bytes(2, byteorder='little'))
                cmd.append(0x7E)  # 帧尾
                
                # 添加调试日志
                # print(f"DEBUG - generate_command - 读取电机转速 - 生成的完整指令: {cmd.hex().upper()}")
                return bytes(cmd)

            # 检查是否是固定指令
            if command_name in self.fixed_commands and command_name != "读取电机转速":
                return self.fixed_commands[command_name]
                
            # 处理可变指令
            if command_name == "查询软硬件版本信息":
                frame_num = kwargs.get('frame_num', 0)
                if frame_num in self.version_query_frames:
                    return self.version_query_frames[frame_num]
                else:
                    raise ValueError(f"无效的查询软硬件版本信息帧编号: {frame_num}")
            elif command_name == "读取电机转速":
                return self._generate_read_motor_speed_command(kwargs.get('content', '1'))
            elif command_name == "写RCU序列号":
                return self._generate_write_sn_command(kwargs.get('internal_sn', ''))
            elif command_name == "回读电子标签":
                return self._generate_read_electronic_tag_command(kwargs.get('frame_num', '0'))
            elif command_name == "写电子标签":
                serial_number = kwargs.get('serial_number', kwargs.get('content', ''))
                frame_num = kwargs.get('frame_num', 0)
                return self._generate_write_electronic_tag_command(serial_number, frame_num)
            elif command_name == "回读模块文件":
                return self._generate_read_file_command('05', kwargs.get('frame_num', '0'))
            elif command_name == "回读天线文件":
                return self._generate_read_file_command('06', kwargs.get('frame_num', '0'))
            elif command_name == "回读合路器信息文件":
                # 新增：回读合路器信息文件指令
                file_type = kwargs.get('file_type', '模块信息文件')
                frame_num = kwargs.get('frame_num', 0)
                return self.generate_read_combiner_info_command(file_type, frame_num)
            elif command_name == "写模块文件":
                file_path = kwargs.get('file_path', '')
                frame_num = kwargs.get('frame_num', 0)
                # 支持用户输入文件类型或使用默认值
                file_type = kwargs.get('file_type', '模块信息文件')
                hex_file_type = self._convert_file_type_to_hex(file_type)
                return self._generate_write_file_command(file_path, hex_file_type, frame_num)
            elif command_name == "写天线文件":
                file_path = kwargs.get('file_path', '')
                frame_num = kwargs.get('frame_num', 0)
                # 支持用户输入文件类型或使用默认值
                file_type = kwargs.get('file_type', '天线信息文件')
                hex_file_type = self._convert_file_type_to_hex(file_type)
                return self._generate_write_file_command(file_path, hex_file_type, frame_num)
            elif command_name == "写合路器信息文件":
                file_path = kwargs.get('file_path', '')
                frame_num = kwargs.get('frame_num', 0)
                # 根据文件类型确定合路器信息文件的类型，默认使用'模块信息文件'
                file_type = kwargs.get('file_type', '模块信息文件')
                hex_file_type = self._convert_file_type_to_hex(file_type)
                return self._generate_write_file_command(file_path, hex_file_type, frame_num)
            elif command_name == "对设备分配地址并建链":
                return self._generate_device_address_command(kwargs.get('internal_sn', ''))
            elif command_name == "设备设置装备模式":
                return self._generate_equipment_mode_command(kwargs.get('mode', True))
            elif command_name == "设置假负载开关":
                return self._generate_dummy_load_command(kwargs.get('enabled', True))
            elif command_name == "设置天线倾角":
                return self._generate_antenna_tilt_command(kwargs.get('angle', 0))
                
            return None
        except Exception as e:
            print(f"DEBUG - generate_command - 生成指令时发生错误: {e}")
            return None
        
    def _generate_write_sn_command(self, internal_sn: str) -> bytes:
        """生成写RCU序列号指令"""
        if len(internal_sn) != 19:
            raise ValueError("内部序列号长度必须为19位")
        
        # 取后17位
        sn_17_digits = internal_sn[-17:]
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 15 00 48 57 81 06"))
        cmd_data.extend(sn_17_digits.encode('ascii'))
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_read_electronic_tag_command(self, frame_num: str) -> bytes:
        """生成回读电子标签指令"""
        try:
            frame = int(frame_num)
            if frame < 0 or frame > 3:
                raise ValueError("帧编号必须在0-3之间")
        except ValueError:
            raise ValueError("无效的帧编号")
            
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 04 00 48 57 11"))
        cmd_data.append(frame)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_read_file_command(self, file_type: str, frame_num: str) -> bytes:
        """生成回读合路器信息文件指令"""
        # print(f"DEBUG - _generate_read_file_command - 输入参数: file_type='{file_type}', frame_num='{frame_num}'")
        try:
            frame = int(frame_num)
            if frame < 0:
                raise ValueError("帧编号不能为负数")
        except ValueError:
            raise ValueError("无效的帧编号")
            
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 05 00 48 57 87"))
        hex_value = int(file_type, 16)
        # print(f"DEBUG - _generate_read_file_command - 转换后的file_type十六进制值: 0x{hex_value:02X}")
        cmd_data.append(hex_value)  # 05为模块文件，06为天线文件
        cmd_data.append(frame)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_write_file_command(self, file_path: str, file_type: str, frame_num: int) -> bytes:
        """生成写合路器信息文件指令（单帧）"""
        # print(f"DEBUG - _generate_write_file_command - 输入参数: file_path='{file_path}', file_type='{file_type}', frame_num={frame_num}")
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 读取文件数据
        with open(file_path, 'rb') as f:
            file_data = f.read()
            
        # 计算该帧的数据范围
        max_frame_size = 0x43  # 每帧最大数据大小
        start_pos = frame_num * max_frame_size
        end_pos = min(start_pos + max_frame_size, len(file_data))
        
        if start_pos >= len(file_data):
            raise ValueError(f"帧编号 {frame_num} 超出数据范围")
            
        frame_data = file_data[start_pos:end_pos]
        data_length = len(frame_data) + 5  # 信息域长度 = 数据长度 + 5
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90"))
        cmd_data.extend(data_length.to_bytes(2, byteorder='little'))  # 信息域长度，低位在前
        cmd_data.extend(bytes.fromhex("48 57 86"))
        hex_value = int(file_type, 16)
        # print(f"DEBUG - _generate_write_file_command - 转换后的file_type十六进制值: 0x{hex_value:02X}")
        cmd_data.append(hex_value)  # 05为模块文件，06为天线文件
        cmd_data.append(frame_num)  # 帧编号
        cmd_data.extend(frame_data)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_device_address_command(self, internal_sn: str) -> bytes:
        """生成对设备分配地址并建链指令"""
        # 取后17位
        rcu_sn = internal_sn[-17:]
        if len(rcu_sn) != 17:
            raise ValueError("RCU序列号长度必须为17位")
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("FF BF 81 F0 18 01 13 48 57"))
        cmd_data.extend(rcu_sn.encode('ascii'))
        cmd_data.extend(bytes.fromhex("02 01 01"))
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_equipment_mode_command(self, enter_mode: bool) -> bytes:
        """生成设备设置装备模式指令"""
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 04 00 48 57 50"))
        cmd_data.append(0x01 if enter_mode else 0x00)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_dummy_load_command(self, enabled: bool) -> bytes:
        """生成设置假负载开关指令"""
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 06 00 48 57 27 21 01"))
        cmd_data.append(0x01 if enabled else 0x00)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def _generate_antenna_tilt_command(self, angle: int) -> bytes:
        """生成设置天线倾角指令"""
        if angle < 0 or angle > 40:
            raise ValueError("倾角必须在0到40之间")
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 33 02 00"))
        # 两字节有符号数，低位在前
        cmd_data.extend(angle.to_bytes(2, byteorder='little', signed=True))
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)
        
    def generate_electronic_tag(self, serial_number: str) -> bytes:
        """生成电子标签内容"""
        if len(serial_number) != 20:
            raise ValueError("序列号长度必须为20位")
            
        # 提取20位序列号的2-10位作为Item（共8位）
        item = serial_number[2:10]
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # 构建电子标签内容
        tag_content = (
            "/$[ArchivesInfo Version]\r\n"
            "/$ArchivesInfoVersion = 3.0\r\n"
            "\r\n"
            "\r\n"
            "[Board Properties]\r\n"
            "BoardType=AIMM20D11v04\r\n"
            f"BarCode={serial_number}\r\n"
            f"Item={item}\r\n"
            "Description=Antenna Accessory,AIMM20D11v04,Antenna Information Management Module,Huawei Antenna,DC 10~30V,AISG2.0,a pair of AISG connectors,1in 1out\r\n"
            f"Manufactured={current_date}\r\n"
            "VendorName=Huawei\r\n"
            "IssueNumber=00\r\n"
            "CLEICode=\r\n"
            "BOM="
        )
        
        # 转换为ASCII码
        ascii_content = tag_content.encode('ascii')
        
        # 添加开头和结尾的十六进制数
        full_tag = bytearray()
        full_tag.extend(bytes.fromhex("9A 86 76 01"))  # 开头
        full_tag.extend(ascii_content)
        full_tag.append(0x00)  
        
        return bytes(full_tag)
        
        
    def debug_crc_validation(self, response: bytes) -> dict:
        """调试CRC验证，返回详细的CRC信息"""
        if len(response) < 6:
            return {"error": "响应长度太短"}
        
        data_part = response[1:-3]  # 排除帧头、CRC和帧尾
        received_crc_bytes = response[-3:-1]  # CRC字节
        received_crc = int.from_bytes(received_crc_bytes, byteorder='little')
        
        if self.crc16_func:
            calculated_crc = self.crc16_func(data_part)
        else:
            calculated_crc = 0
        
        return {
            "response_hex": response.hex().upper(),
            "data_part": data_part.hex().upper(),
            "received_crc_bytes": received_crc_bytes.hex().upper(),
            "received_crc": f"0x{received_crc:04X}",
            "calculated_crc": f"0x{calculated_crc:04X}",
            "crc_match": received_crc == calculated_crc,
            "data_length": len(data_part)
        }
        
    def validate_response(self, command_name: str, response: bytes, debug_mode: bool = False, **kwargs) -> bool:
        """验证响应是否正确"""
        # 首先检查是否是电源指令
        power_commands = self.power_handler.get_power_command_list()
        if command_name in power_commands:
            return self._validate_power_response(command_name, response, debug_mode, **kwargs)

        # 检查响应是否为空
        if not response:
            if debug_mode:
                print("DEBUG - 响应为空")
            return False
            
        # 检查帧头帧尾
        if response[0] != 0x7E or response[-1] != 0x7E:
            if debug_mode:
                print(f"DEBUG - 帧头帧尾错误: {response.hex().upper()}")
            return False
            
        # 特殊处理：查询软硬件版本信息指令
        if command_name == "查询软硬件版本信息":
            # 验证基本格式：帧头(7E) + 地址(01) + 类型(30) + 功能码(05) + 数据长度(44) + ...
            if len(response) < 6:
                if debug_mode:
                    print(f"DEBUG - 查询软硬件版本信息响应长度不足: {len(response)}")
                return False
                
            # 验证帧头、地址、类型和功能码
            if response[0] != 0x7E or response[1] != 0x01 or response[2] != 0x30 or response[3] != 0x05:
                if debug_mode:
                    print(f"DEBUG - 查询软硬件版本信息响应格式错误: {response[:4].hex().upper()}")
                return False
                
            # 验证数据长度
            # if len(response) < 6 or response[4] != 0x44:
            #     if debug_mode:
            #         print(f"DEBUG - 查询软硬件版本信息响应数据长度错误: {response[4] if len(response) > 4 else 'N/A'}")
            #     return False
                
            # 验证帧尾
            if response[-1] != 0x7E:
                if debug_mode:
                    print(f"DEBUG - 查询软硬件版本信息响应帧尾错误: {response[-1:].hex().upper()}")
                return False
                
            # 验证CRC
            received_crc = int.from_bytes(response[-3:-1], byteorder='little')
            calculated_crc = self._calculate_crc(response[1:-3])
            
            if received_crc != calculated_crc:
                if debug_mode:
                    print(f"DEBUG - 查询软硬件版本信息响应CRC校验失败")
                return False
                
            return True
            
        # 特殊处理：设置天线倾角指令
        if command_name == "设置天线倾角":
            # 如果收到 7E 01 30 31 01 00 00 E0 2D 7E，表示天线校准完毕，需要重发指令
            if response == bytes.fromhex("7E 01 30 31 01 00 00 E0 2D 7E"):
                if debug_mode:
                    print("DEBUG - 收到天线校准完成响应，需要重发指令")
                # 设置标志，通知外部需要重发指令
                self._needs_resend = True
                # 设置标志，表示需要发送响应帧
                self.command_state["waiting_for_response_frame"] = True
                return True
            # 如果收到 7E 01 30 33 01 00 00 96 14 7E，表示设置成功
            elif response == bytes.fromhex("7E 01 30 33 01 00 00 96 14 7E"):
                if debug_mode:
                    print("DEBUG - 收到天线倾角设置成功响应")
                self._needs_resend = False
                return True
            else:
                if debug_mode:
                    print(f"DEBUG - 天线倾角设置响应无效: {response.hex().upper()}")
                return False
                
        # 检查基本帧结构
        if len(response) < 6 or response[0] != 0x7E or response[-1] != 0x7E:
            return False
            
        # 验证CRC
        received_crc = int.from_bytes(response[-3:-1], byteorder='little')
        calculated_crc = self._calculate_crc(response[1:-3])
        
        if received_crc != calculated_crc:
            return False
            
        # 特定指令的额外验证 - 优先处理特殊指令
        if command_name == "天线校准命令":
            # 天线校准命令有两种有效响应：
            # 1. 正常响应：7E 01 30 31 01 00 00 E0 2D 7E
            # 2. 中间帧前置：7E 01 31 95 36 7E（也视为成功）
            if response == self.expected_responses.get("天线校准命令", b""):
                return True  # 正常响应
            if self.is_special_frame(response):
                return True  # 中间帧前置也视为成功
            return False
            
        if command_name == "查询软硬件版本信息":
            # 检查是否是帧0的响应
            if response == self.version_query_responses["frame_0"]:
                return True
            # 检查是否是帧1的响应（完整版本信息）
            if response == self.version_query_responses["frame_1"]:
                return True
            # 对于多帧指令，还可能有其他响应格式
            return False
            
        # 检查是否是固定响应（排除已经特殊处理的指令）
        if command_name in self.expected_responses:
            return response == self.expected_responses[command_name]
            
        # 其他特定指令的额外验证
        if command_name == "回读温度信息":
            # 响应格式：7E 01 30 90 06 00 48 57 89 00 （两字节有符号数，低位在前） CRC16（低位，高位） 7E
            return (len(response) == 15 and 
                    response[1:10] == bytes.fromhex("01 30 90 06 00 48 57 89 00"))
        elif command_name == "读取电机转速":
            # 响应格式：7E 01 30 90 05 00 48 57 90 00 1字节无符号数 CRC16（低位，高位） 7E
            return (len(response) == 14 and 
                    response[1:10] == bytes.fromhex("01 30 90 05 00 48 57 90 00"))
        elif command_name == "写电子标签":
            # 响应格式：7E 01 30 90 05 00 48 57 10 00 帧编号 CRC16（低位，高位） 7E
            frame_num = kwargs.get('frame_num', 0)
            expected_frame = bytes([frame_num])
            
            # 调试模式下输出详细信息
            if debug_mode:
                print(f"DEBUG - 写电子标签响应验证:")
                print(f"  响应长度: {len(response)}")
                print(f"  期望帧编号: {frame_num}")
                print(f"  响应帧编号: {response[10] if len(response) > 10 else 'N/A'}")
                print(f"  响应前10字节: {response[1:10].hex().upper() if len(response) >= 10 else 'N/A'}")
                print(f"  期望前10字节: {bytes.fromhex('01 30 90 05 00 48 57 10 00').hex().upper()}")
            
            is_valid = (len(response) == 14 and 
                       response[1:10] == bytes.fromhex("01 30 90 05 00 48 57 10 00") and
                       response[10:11] == expected_frame)
                       
            if debug_mode:
                print(f"  验证结果: {is_valid}")
                
            return is_valid
        elif command_name == "回读RCU序列号":
            # 响应格式：7E 01 30 90 16 00 48 57 80 00 06 （17位ASCII码） CRC16（低位，高位） 7E
            return (len(response) == 31 and 
                    response[1:11] == bytes.fromhex("01 30 90 16 00 48 57 80 00 06"))
        elif command_name == "回读备份序列号":
            # 响应格式：7E 01 30 90 17 00 48 57 EB 00 48 57 （17位ASCII码） CRC16（低位，高位） 7E
            return (len(response) == 32 and 
                    response[1:12] == bytes.fromhex("01 30 90 17 00 48 57 EB 00 48 57"))
        elif command_name in ["回读模块文件", "回读天线文件"]:
            # 响应格式：7E 01 30 90 信息域长度 48 57 87 00 05/06 帧编号 数据 CRC16（低位，高位） 7E
            file_type = 0x05 if command_name == "回读模块文件" else 0x06
            frame_num = kwargs.get('frame_num', 0)
            if len(response) < 14:
                return False
            expected_header = bytes.fromhex("01 30 90")
            expected_mid = bytes.fromhex("48 57 87 00") + bytes([file_type, frame_num])
            return (response[1:4] == expected_header and
                    response[6:12] == expected_mid)
        elif command_name == "回读合路器信息文件":
            # 新增：回读合路器信息文件响应验证
            # 响应格式：7E 01 30 90 信息域长度（48 00，低位在前） 48 57 87 00 05/06 帧编号 数据 CRC16（低位，高位） 7E
            file_type = kwargs.get('file_type', '模块信息文件')
            frame_num = kwargs.get('frame_num', 0)
            
            # 1. 验证基本格式
            if not self.validate_read_combiner_response(response, file_type, frame_num):
                if debug_mode:
                    print(f"DEBUG - 回读合路器信息文件 - 基本格式验证失败")
                return False
                
            # 2. 解析数据
            parsed_data = self.parse_read_combiner_response_data(response)
            if not parsed_data["valid"]:
                if debug_mode:
                    print(f"DEBUG - 回读合路器信息文件 - 数据解析失败")
                return False
                
            # 3. 如果提供了文件路径，进行数据对比
            file_path = kwargs.get('file_path', '')
            if file_path and parsed_data.get("file_data"):
                frame_data = parsed_data["file_data"]
                if not self.compare_frame_data_with_file(frame_data, file_path, frame_num):
                    if debug_mode:
                        print(f"DEBUG - 回读合路器信息文件 - 数据对比失败")
                    return False
                elif debug_mode:
                    print(f"DEBUG - 回读合路器信息文件 - 数据对比成功")
            
            # 4. 验证成功后，更新当前帧编号（确保按顺序递增）
            # 从响应中获取实际的帧编号
            response_frame_num = parsed_data.get("frame_num", 0)
            
            # 强制帧编号按顺序递增，忽略设备返回的帧编号
            self.command_state["current_frame"] = frame_num + 1
            
            if debug_mode:
                if response_frame_num != frame_num:
                    print(f"DEBUG - 回读合路器信息文件 - 设备返回的帧编号({response_frame_num})与期望({frame_num})不匹配，但仍按顺序递增")
                else:
                    print(f"DEBUG - 回读合路器信息文件 - 帧编号递增: {frame_num} -> {frame_num + 1}")
            
            return True
        elif command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            # 正常情况响应格式：7E 01 30 90 06 00 48 57 86 00 05/06 帧编号 CRC16（低位，高位） 7E
            
            # 确定期望的文件类型
            user_file_type = kwargs.get('file_type')
            if user_file_type:
                try:
                    # 用户指定了文件类型，使用转换后的值
                    hex_file_type = self._convert_file_type_to_hex(user_file_type)
                    expected_file_type = int(hex_file_type, 16)
                except ValueError:
                    # 如果用户文件类型无效，使用默认值
                    expected_file_type = 0x05 if command_name == "写模块文件" else 0x06
            else:
                # 没有用户指定，使用默认值
                if command_name == "写模块文件":
                    expected_file_type = 0x05
                elif command_name == "写天线文件":
                    expected_file_type = 0x06
                else:  # 写合路器信息文件，默认模块类型
                    expected_file_type = 0x05
                    
            frame_num = kwargs.get('frame_num', 0)
            
            # 构建期望的响应前缀（不包括CRC和帧尾）
            expected_prefix = (bytes.fromhex("7E 01 30 90 06 00 48 57 86 00") + 
                             bytes([expected_file_type, frame_num]))
            
            # 检查是否是正常响应格式（包括CRC验证）
            if len(response) == 15 and response[:12] == expected_prefix:
                # 验证CRC
                received_crc = int.from_bytes(response[-3:-1], byteorder='little')
                calculated_crc = self._calculate_crc(response[1:-3])
                if received_crc == calculated_crc:
                    return True
                    
            # 检查是否是特殊帧（最后一帧的特殊情况）
            return self.is_special_frame(response)
        elif command_name == "对设备分配地址并建链":
            # 响应格式：7E 01 BF 81 F0 18 01 13 48 57 （RCU序列号17位ASCII） 04 01 01 CRC16（低位，高位） 7E
            internal_sn = kwargs.get('internal_sn', '')
            if len(internal_sn) < 17:
                if debug_mode:
                    print(f"DEBUG - internal_sn长度不足17位: {internal_sn}")
                return False
            # 取后17位
            rcu_sn = internal_sn[-17:]
            expected_header = bytes.fromhex("7E 01 BF 81 F0 18 01 13 48 57")
            expected_tail = bytes.fromhex("04 01 01")
            
            if debug_mode:
                print(f"DEBUG - 验证对设备分配地址并建链响应:")
                print(f"  响应: {response.hex().upper()}")
                print(f"  期望头部: {expected_header.hex().upper()}")
                print(f"  期望序列号: {rcu_sn}")
                print(f"  期望尾部: {expected_tail.hex().upper()}")
                print(f"  响应长度: {len(response)}")
                if len(response) >= 27:
                    print(f"  响应中的序列号: {response[10:27].decode('ascii', errors='ignore')}")
            
            is_valid = (len(response) == 33 and
                    response[:10] == expected_header and
                    response[10:27] == rcu_sn.encode('ascii') and
                    response[27:30] == expected_tail)
                    
            if debug_mode:
                print(f"  验证结果: {is_valid}")
            
            return is_valid
        elif command_name == "Flash MINI自检":
            # Flash MINI自检只有正常响应，没有特殊回复
            return response == self.expected_responses.get("Flash MINI自检", b"")
            
        # 特殊处理：响应帧验证
        if command_name == "响应帧":
            expected_echo = bytes.fromhex("7E 01 73 83 57 7E")
            return response == expected_echo
            
        return True
        
    def parse_response_data(self, command_name: str, response: bytes, **kwargs) -> dict:
        """解析响应数据，返回具体信息"""
        result = {"valid": self.validate_response(command_name, response)}
        
        if not result["valid"]:
            return result
            
        if command_name == "查询软硬件版本信息":
            # 确保响应长度足够
            if len(response) < 7:  # 至少需要帧头+地址+类型+功能码+数据长度+CRC+帧尾
                return {"error": "响应数据长度不足", "valid": False}
            
            # 检查基本格式
            if response[0] != 0x7E or response[1] != 0x01 or response[2] != 0x30 or response[3] != 0x05:
                return {"error": "响应格式错误", "valid": False}
            
            # 提取数据长度
            data_length = response[4]
            if len(response) < data_length + 7:  # 帧头(1) + 地址(1) + 类型(1) + 功能码(1) + 长度(1) + 数据(N) + CRC(2) + 帧尾(1)
                return {"error": f"数据长度不匹配: 期望 {data_length}, 实际 {len(response) - 7}", "valid": False}
            
            # 提取数据部分 (跳过帧头、地址、类型、功能码、长度)
            data_bytes = response[6:6+data_length]
            
            # 将数据转换为ASCII字符串
            try:
                ascii_data = data_bytes.decode('ascii', errors='replace')
                result.update({
                    "response_type": "version_info",
                    "message": "收到软硬件版本信息",
                    "data_length": data_length,
                    "raw_data": data_bytes.hex().upper(),
                    "ascii_data": ascii_data,
                    "version_info": ascii_data  # 软硬件版本信息
                })
            except Exception as e:
                result.update({
                    "response_type": "version_info",
                    "message": f"ASCII解码失败: {str(e)}",
                    "data_length": data_length,
                    "raw_data": data_bytes.hex().upper()
                })
            
            return result
        elif command_name == "回读温度信息":
            if len(response) >= 14:
                temp_bytes = response[10:12]
                temperature = int.from_bytes(temp_bytes, byteorder='little', signed=True)
                result["temperature"] = temperature
        elif command_name == "读取电机转速":
            if len(response) >= 13:
                speed = response[10]
                result["motor_speed"] = speed
        elif command_name == "回读RCU序列号":
            if len(response) >= 30:
                sn_bytes = response[11:28]
                result["serial_number"] = sn_bytes.decode('ascii', errors='ignore')
        elif command_name == "回读备份序列号":
            if len(response) >= 32:
                sn_bytes = response[12:29]
                result["backup_serial_number"] = sn_bytes.decode('ascii', errors='ignore')
        elif command_name == "回读电子标签":
            if len(response) >= 14:
                frame_num = response[10] if len(response) > 10 else 0
                data_length = int.from_bytes(response[4:6], byteorder='little') if len(response) > 6 else 0
                if data_length > 4 and len(response) >= 11 + data_length - 4:
                    tag_data = response[11:11 + data_length - 4]
                    result.update({
                        "frame_num": frame_num,
                        "data_length": data_length,
                        "tag_data": tag_data,
                        "tag_content": tag_data.decode('ascii', errors='ignore')
                    })
        elif command_name in ["回读模块文件", "回读天线文件"]:
            if len(response) >= 14:
                data_length = int.from_bytes(response[4:6], byteorder='little') if len(response) > 6 else 0
                frame_num = response[11] if len(response) > 11 else 0
                if data_length > 6 and len(response) >= 12 + data_length - 6:
                    file_data = response[12:12 + data_length - 6]
                    result.update({
                        "frame_num": frame_num,
                        "data_length": data_length,
                        "file_data": file_data,
                        "file_type": "模块文件" if command_name == "回读模块文件" else "天线文件"
                    })
        elif command_name == "回读合路器信息文件":
            # 新增：回读合路器信息文件响应解析
            parsed_data = self.parse_read_combiner_response_data(response)
            result.update(parsed_data)
            
            # 如果提供了文件路径，进行数据比较验证
            file_path = kwargs.get('file_path', '')
            if file_path and parsed_data.get("valid") and parsed_data.get("file_data"):
                frame_data = parsed_data["file_data"]
                frame_num = parsed_data.get("frame_num", 0)
                is_match = self.compare_frame_data_with_file(frame_data, file_path, frame_num)
                result["data_validation"] = "匹配" if is_match else "不匹配"
                result["comparison_result"] = is_match
                result["valid"] = result["valid"] and is_match  # 更新整体验证结果
        elif command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            # 解析写文件响应中的帧编号
            if len(response) >= 12:
                frame_num = response[11] if response[11:12] != b'' else 0
                result["frame_num"] = frame_num
        elif command_name == "写电子标签":
            # 解析写电子标签响应中的帧编号
            if len(response) >= 11:
                frame_num = response[10] if response[10:11] != b'' else 0
                result["frame_num"] = frame_num
                
        return result
        
    def _calculate_crc(self, data: bytes) -> int:
        """计算CRC16校验码"""
        crc = self.crc16_func(data)
        return crc
        
    def escape_command(self, data: bytes) -> bytes:
        """
        转义指令 - HDLC帧转义规则
        7E -> 7D 5E (帧定界符转义)
        7D -> 7D 5D (转义符自身转义)
        注意：帧头和帧尾的7E不进行转义
        """
        escaped = bytearray()
        for i, byte in enumerate(data):
            # 不转义帧头和帧尾的7E
            if (i == 0 or i == len(data)-1) and byte == 0x7E:
                escaped.append(byte)
            elif byte == 0x7E:
                # 7E -> 7D 5E
                escaped.extend([0x7D, 0x5E])
            elif byte == 0x7D:
                # 7D -> 7D 5D
                escaped.extend([0x7D, 0x5D])
            else:
                escaped.append(byte)
        return bytes(escaped)
        
    def unescape_response(self, data: bytes) -> bytes:
        """
        反转义响应 - HDLC帧反转义规则
        7D 5E -> 7E (恢复帧定界符)
        7D 5D -> 7D (恢复转义符自身)
        """
        unescaped = bytearray()
        i = 0
        while i < len(data):
            if i < len(data)-1 and data[i] == 0x7D:
                if data[i+1] == 0x5E:
                    # 7D 5E -> 7E
                    unescaped.append(0x7E)
                    i += 2
                elif data[i+1] == 0x5D:
                    # 7D 5D -> 7D
                    unescaped.append(0x7D)
                    i += 2
                else:
                    # 如果7D后面不是5E或5D，按原样保留（可能是错误数据）
                    unescaped.append(data[i])
                    i += 1
            else:
                unescaped.append(data[i])
                i += 1
        return bytes(unescaped)
        
    def generate_file_command(self, file_path: str, file_type: str) -> List[bytes]:
        """生成文件传输指令列表"""
        # print(f"DEBUG - generate_file_command - 输入参数: file_path='{file_path}', file_type='{file_type}'")
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        commands = []
        with open(file_path, 'rb') as f:
            file_data = f.read()
            
        # 使用统一的文件类型转换方法
        hex_file_type = self._convert_file_type_to_hex(file_type)
        cmd_type = int(hex_file_type, 16)
        # print(f"DEBUG - generate_file_command - 转换后的file_type: hex='{hex_file_type}', int=0x{cmd_type:02X}")
            
        # 分帧处理，每帧最大0x43个字节
        max_frame_size = 0x43   
        frame_num = 0
        
        for i in range(0, len(file_data), max_frame_size):
            frame_data = file_data[i:i+max_frame_size]
            data_length = len(frame_data) + 5  # 信息域长度 = 数据长度 + 5
            
            # 构建指令内容（不包括帧头和帧尾）
            cmd_data = bytearray()
            cmd_data.extend(bytes.fromhex("01 10 90"))
            cmd_data.extend(data_length.to_bytes(2, byteorder='little'))  # 信息域长度
            cmd_data.extend(bytes.fromhex("48 57 86"))
            cmd_data.append(cmd_type)  # 文件类型
            cmd_data.append(frame_num)  # 帧编号
            cmd_data.extend(frame_data)
            
            # 计算CRC
            crc = self._calculate_crc(cmd_data)
            
            # 构建完整指令
            cmd = bytearray()
            cmd.append(0x7E)  # 帧头
            cmd.extend(cmd_data)
            cmd.extend(crc.to_bytes(2, byteorder='little'))
            cmd.append(0x7E)  # 帧尾
            
            commands.append(bytes(cmd))
            frame_num += 1
            
        return commands
        
    def is_special_frame(self, response: bytes) -> bool:
        """
        检查是否是特殊帧（中间帧前置）
        
        中间帧机制说明：
        - 中间帧类似中断操作
        - 当上位机收到中间帧前置（特殊帧）时，进入中断模式
        - 在中断模式下，持续发送中间帧
        - 直到接收到的数据不再是中间帧前置，此时退出中断模式
        - 退出中断后，对最后收到的数据进行正常校验
        """
        return response == self.special_frame
        
    def is_middle_frame_needed(self, response: bytes) -> bool:
        """检查是否需要发送中间帧"""
        return self.is_special_frame(response)
        
    def get_middle_frame(self) -> bytes:
        """获取中间帧"""
        return self.middle_frame
        
    def get_response_frame(self) -> bytes:
        """获取响应帧"""
        return self.response_frame
        
    def needs_response_frame(self, command_name: str) -> bool:
        """检查指令是否需要发送响应帧"""
        # 首先检查是否在no_response_commands列表中
        if command_name in self.no_response_commands:
            return False
            
        # 特殊处理：设置天线倾角指令需要发送响应帧
        if command_name == "设置天线倾角":
            return True
            
        # 特殊情况：天线文件最后一帧不需要响应帧
        if command_name in ["写天线文件", "写合路器信息文件"]:
            # 检查是否是最后一帧
            if (self.command_state["current_frame"] + 1) >= self.command_state["total_frames"]:
                # 检查文件类型是否为天线文件
                file_type = self.command_state["command_params"].get("file_type", "")
                time.sleep(1.0)
                if file_type == "天线信息文件" or file_type == "06":
                    return False
                    
        return True
        
    def get_version_query_commands(self) -> List[bytes]:
        """获取查询软硬件版本信息的指令序列"""
        return [
            self.fixed_commands["查询软硬件版本信息"],
            self.response_frame,  # 发送响应帧
            self._internal_commands["查询软硬件版本信息_第二次"]
        ]
        
    def is_multi_frame_command(self, command_name: str) -> bool:
        """检查指令是否需要多帧处理"""
        multi_frame_commands = {
            "写电子标签", 
            "写合路器信息文件", 
            "写模块文件", 
            "写天线文件",
            "回读合路器信息文件"  # 新增
        }
        return command_name in multi_frame_commands
        
    def get_total_frames_count(self, command_name: str, **kwargs) -> int:
        """获取指令的总帧数"""
        if command_name == "查询软硬件版本信息":
            return 1  # 单帧指令
        elif command_name == "写电子标签":
            serial_number = kwargs.get('serial_number', kwargs.get('content', ''))
            if serial_number:
                # 对于20位序列号，重新计算电子标签数据大小
                tag_data = self.generate_electronic_tag(serial_number)
                frame_size = 0x62  # 修正为实际的帧大小
                return (len(tag_data) + frame_size - 1) // frame_size  # 向上取整
        elif command_name in ["写合路器信息文件", "写模块文件", "写天线文件"]:
            file_path = kwargs.get('file_path', '')
            if file_path and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                frame_size = 0x43  # 每帧实际数据大小
                return (file_size + frame_size - 1) // frame_size  # 向上取整
        elif command_name == "回读合路器信息文件":
            # 对于回读指令，帧数通常由设备决定，这里返回一个估计值
            # 可以根据实际需要动态调整
            file_path = kwargs.get('file_path', '')
            if file_path and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                frame_size = 0x47 - 6  # 每帧实际数据大小(0x48字节信息域长度 - 6字节固定头)
                return (file_size + frame_size - 1) // frame_size  # 向上取整
            return 10  # 默认估计值，实际运行时会动态调整
        return 1
        
    def is_last_frame_special_response(self, command_name: str, response: bytes) -> bool:
        """检查是否是最后一帧的特殊响应"""
        if command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            return self.is_special_frame(response)
        return False
        
    def get_expected_final_response(self, command_name: str) -> bytes:
        """获取特殊情况下的最终期望响应"""
        if command_name == "写模块文件":
            return bytes.fromhex("7E 01 30 90 06 00 48 57 86 00 05 26 1F 38 7E")
        elif command_name == "写天线文件":
            return bytes.fromhex("7E 01 30 90 06 00 48 57 86 00 06 0E 3D BF 7E")
        return b""
        
    def should_continue_middle_frame(self, command_name: str, response: bytes) -> bool:
        """
        判断是否应该继续发送中间帧（中断模式）
        
        天线校准逻辑：
        - 发送天线校准命令
        - 收到中间帧前置，发送中间帧
        - 无论收到什么，都退出天线校准指令
        - 发送响应帧
        """
        
        # 特殊处理：天线校准命令
        if command_name == "天线校准命令":
            # 天线校准的特殊逻辑：只要已经发送过一次中间帧，就不再继续
            if self.command_state.get("middle_frame_count", 0) >= 1:
                return False  # 已经发送过中间帧，立即退出
            
            # 如果是第一次收到中间帧前置，继续发送中间帧
            if self.is_special_frame(response):
                return True  # 发送第一个中间帧
            else:
                return False  # 收到非中间帧前置，退出
        
        # 主要逻辑：中间帧中断机制（其他指令）
        if self.is_special_frame(response):
            return True  # 继续中断模式，发送中间帧
        
        # 特殊指令的额外处理逻辑（某些指令可能有特殊的终止条件）
        if command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            # 文件写入指令：如果收到最终期望响应，明确停止
            expected_final = self.get_expected_final_response(command_name)
            if expected_final and response == expected_final:
                return False  # 明确退出中断模式
            # 其他情况下，如果不是特殊帧，也退出中断模式
            return False
        
        # 对于大部分指令：收到非特殊帧数据就退出中断模式
        return False
        
    def get_next_frame_command(self) -> Optional[bytes]:
        """获取下一帧指令"""
        if not self.command_state["current_command"]:
            return None
            
        command_name = self.command_state["current_command"]
        frame_num = self.command_state["current_frame"]
        params = self.command_state["command_params"].copy()
        params["frame_num"] = frame_num
        
        return self.generate_command(command_name, **params)
        
    def get_command_state_info(self) -> dict:
        """获取当前指令状态信息"""
        return self.command_state.copy()

    def reset_command_state(self):
        """重置指令状态"""
        self.command_state = {
            "current_command": None,
            "current_frame": 0,
            "total_frames": 0,
            "waiting_for_response_frame": False,
            "waiting_for_middle_frame": False,
            "send_response_frame_next": False,
            "middle_frame_count": 0,
            "max_middle_frame_attempts": 50,  # 最大中间帧尝试次数
            "command_params": {}
        }
        
    def start_command_sequence(self, command_name: str, **kwargs):
        """开始指令序列"""
        self.reset_command_state()
        self.command_state["current_command"] = command_name
        self.command_state["command_params"] = kwargs
        
        # 为特殊指令设置中间帧尝试次数
        if command_name == "天线校准命令":
            # 天线校准使用正常的should_continue_middle_frame逻辑，不依赖强制退出
            self.command_state["max_middle_frame_attempts"] = 50  # 使用标准值，避免过早触发强制退出
        elif command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            self.command_state["max_middle_frame_attempts"] = 100  # 文件传输可能需要更多次数
        else:
            self.command_state["max_middle_frame_attempts"] = 50   # 默认值
        
        if self.is_multi_frame_command(command_name):
            self.command_state["total_frames"] = self.get_total_frames_count(command_name, **kwargs)
        else:
            self.command_state["total_frames"] = 1

    def _generate_write_electronic_tag_command(self, serial_number: str, frame_num: int = 0) -> bytes:
        """生成写电子标签指令（单帧）"""
        if not serial_number:
            raise ValueError("序列号不能为空")
            
        # 验证序列号长度为20位
        if len(serial_number) != 20:
            raise ValueError("序列号长度必须为20位")
            
        # 生成完整的电子标签数据
        tag_data = self.generate_electronic_tag(serial_number)
        
        # 计算该帧的数据范围
        frame_size = 0x62  # 每帧98字节
        start_pos = frame_num * frame_size
        end_pos = min(start_pos + frame_size, len(tag_data))
        
        if start_pos >= len(tag_data):
            raise ValueError(f"帧编号 {frame_num} 超出数据范围")
            
        frame_data = tag_data[start_pos:end_pos]
        data_length = len(frame_data) + 4  # 信息域长度 = 数据长度 + 4
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90"))
        cmd_data.extend(data_length.to_bytes(2, byteorder='little'))  # 信息域长度，低位在前
        cmd_data.extend(bytes.fromhex("48 57 10"))
        cmd_data.append(frame_num)  # 帧编号
        cmd_data.extend(frame_data)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾

        return bytes(cmd)

    def send_power_command_with_interval(self, serial_manager, command_name: str, interval_ms: int = 10, **kwargs) -> List[Optional[bytes]]:
        """
        发送电源指令，多个字符串指令挨个发送，每个指令间至少指定间隔
        :param serial_manager: 串口管理器
        :param command_name: 指令名称
        :param interval_ms: 指令间间隔时间（毫秒），默认10ms
        :param kwargs: 指令参数
        :return: 响应列表
        """
        # 检查是否是电源指令
        power_commands = self.power_handler.get_power_command_list()
        if command_name not in power_commands:
            if self.logger_manager:
                self.logger_manager.log_error(f"'{command_name}' 不是电源指令", "command")
            return []

        # 获取电源指令列表
        command_list = self.power_handler.generate_power_command_list(command_name, **kwargs)
        if not command_list:
            if self.logger_manager:
                self.logger_manager.log_error(f"无法生成电源指令列表: {command_name}", "command")
            return []

        # 使用串口管理器发送带间隔的指令序列
        # 对于切换供电开关等不需要响应的指令，使用0.05s超时
        timeout = 0.05 if command_name == "切换供电开关" else 0.1
        return serial_manager.send_power_command_sequence_with_interval(
            command_list, interval_ms=interval_ms, timeout_per_command=timeout
        )

    def get_all_commands(self) -> Dict[str, str]:
        """获取所有支持的指令列表"""
        commands = {
            # 固定指令
            "广播进入装备模式": "固定指令",
            "广播恢复出厂序列号": "固定指令", 
            "默认序列号分配地址": "固定指令",
            "写温度信息": "固定指令",
            "回读温度信息": "固定指令",
            "Flash MINI自检": "固定指令",
            "查询软硬件版本信息": "多帧指令",
            "设备1分配地址并建立连接": "固定指令",
            "天线校准命令": "固定指令",
            "读取电机转速": "固定指令",
            "设备2分配地址并建立连接": "固定指令",
            "备份序列号": "固定指令",
            "回读RCU序列号": "固定指令",
            "回读备份序列号": "固定指令",
            "响应帧": "固定指令",
            "中间帧": "固定指令",
            
            # 可变指令
            "写RCU序列号": "可变指令(需要internal_sn参数)",
            "回读电子标签": "可变指令(需要frame_num参数)",
            "写电子标签": "多帧指令(需要serial_number参数)",
            "回读模块文件": "可变指令(需要frame_num参数)",
            "回读天线文件": "可变指令(需要frame_num参数)",
            
            # 新增：回读合路器信息文件指令（总指令）
            "回读合路器信息文件": "多帧指令(总指令,需要file_type和file_path参数)",
            
            # 合路器信息文件指令（总指令）
            "写合路器信息文件": "多帧指令(总指令,需要file_path和file_type参数)",
            
            # 合路器信息文件子指令
            "写模块文件": "多帧指令(写合路器信息文件子指令,需要file_path参数)",
            "写天线文件": "多帧指令(写合路器信息文件子指令,需要file_path参数)",
            
            # 其他指令
            "对设备分配地址并建链": "可变指令(需要internal_sn参数)",
            "设备设置装备模式": "可变指令(需要mode参数)",
            "设置假负载开关": "可变指令(需要enabled参数)",
            "设置天线倾角": "可变指令(需要angle参数)"
        }

        # 添加电源指令
        power_commands = self.power_handler.get_power_command_list()
        commands.update(power_commands)
        return commands

    def _validate_power_response(self, command_name: str, response: bytes, debug_mode: bool = False, **kwargs) -> bool:
        """验证电源指令响应 - 电源指令发送后不需要跟响应帧"""
        try:
            # 电源指令发送后不需要响应帧验证，直接返回True
            if debug_mode:
                print(f"DEBUG - 电源指令 '{command_name}' 不需要响应验证，直接返回成功")

            # 对于特殊的查询指令，如果确实收到了响应，可以尝试解析
            if response and command_name in ["查询上位机状态", "查询供电开关", "读取电流值", "读取电压值"]:
                parsed_result = self.power_handler.parse_power_response(response, command_name)
                if debug_mode:
                    print(f"DEBUG - 电源指令响应解析结果: {parsed_result}")
                # 即使解析失败也不影响指令执行结果

            # 电源指令发送成功即认为执行成功，不依赖响应验证
            return True

        except Exception as e:
            if debug_mode:
                print(f"DEBUG - 电源指令响应处理异常: {e}")
            # 即使出现异常也返回True，因为电源指令不依赖响应
            return True

    def get_command_parameters(self, command_name: str) -> Dict[str, str]:
        """获取指定指令所需的参数"""
        param_mapping = {
            "写RCU序列号": {"internal_sn": "19位内部序列号"},
            "回读电子标签": {"frame_num": "帧编号(0-3)"},
            "写电子标签": {"serial_number": "20位序列号", "frame_num": "帧编号(可选,默认0)"},
            "回读模块文件": {"frame_num": "帧编号"},
            "回读天线文件": {"frame_num": "帧编号"},
            
            # 新增：回读合路器信息文件
            "回读合路器信息文件": {
                "file_type": "文件类型(必需,支持'模块信息文件'、'天线信息文件'、'05'、'06')",
                "frame_num": "帧编号(可选,默认0)",
                "file_path": "对比文件路径(可选,用于数据验证)"
            },
            
            # 总指令 - 写合路器信息文件
            "写合路器信息文件": {
                "file_path": "文件路径", 
                "frame_num": "帧编号(可选,默认0)", 
                "file_type": "文件类型(必需,支持'模块信息文件'、'天线信息文件'、'05'、'06')"
            },
            
            # 子指令 - 有默认文件类型，但也支持自定义
            "写模块文件": {
                "file_path": "文件路径", 
                "frame_num": "帧编号(可选,默认0)", 
                "file_type": "文件类型(可选,默认'模块信息文件',也支持'天线信息文件'、'05'、'06')"
            },
            "写天线文件": {
                "file_path": "文件路径", 
                "frame_num": "帧编号(可选,默认0)", 
                "file_type": "文件类型(可选,默认'天线信息文件',也支持'模块信息文件'、'05'、'06')"
            },
            
            "对设备分配地址并建链": {"internal_sn": "17位RCU序列号"},
            "设备设置装备模式": {"mode": "True进入/False退出装备模式"},
            "设置假负载开关": {"enabled": "True开启/False关闭假负载"},
            "设置天线倾角": {"angle": "倾角值(-32768到32767)"},

            # 电源指令参数
            "读取电流值": {"port": "端口号(如1、2等)"},
            "读取电压值": {"port": "端口号(如1、2等)"}
        }
        return param_mapping.get(command_name, {})
        
    def validate_command_parameters(self, command_name: str, **kwargs) -> Tuple[bool, str]:
        """验证指令参数是否正确"""
        if command_name == "写RCU序列号":
            internal_sn = kwargs.get('internal_sn', '')
            if not internal_sn or len(internal_sn) != 19:
                return False, "内部序列号长度必须为19位"
        elif command_name == "回读电子标签":
            frame_num = kwargs.get('frame_num', '0')
            try:
                frame = int(frame_num)
                if frame < 0 or frame > 3:
                    return False, "帧编号必须在0-3之间"
            except ValueError:
                return False, "无效的帧编号"
        elif command_name == "写电子标签":
            serial_number = kwargs.get('serial_number', kwargs.get('content', ''))
            if not serial_number or len(serial_number) != 20:
                return False, "序列号长度必须为20位"
        elif command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            file_path = kwargs.get('file_path', '')
            if not file_path:
                return False, "文件路径不能为空"
            if not os.path.exists(file_path):
                return False, f"文件不存在: {file_path}"
            
            # 验证文件类型参数（如果提供）
            file_type = kwargs.get('file_type')
            if file_type is not None:
                try:
                    self._convert_file_type_to_hex(file_type)
                except ValueError as e:
                    return False, str(e)
        elif command_name == "回读合路器信息文件":
            # 验证文件类型参数
            file_type = kwargs.get('file_type', '')
            if not file_type:
                return False, "文件类型不能为空"
            try:
                self._convert_file_type_to_hex(file_type)
            except ValueError as e:
                return False, str(e)
                
            # 验证帧编号
            frame_num = kwargs.get('frame_num', '0')
            try:
                frame = int(frame_num)
                if frame < 0:
                    return False, "帧编号不能为负数"
            except ValueError:
                return False, "无效的帧编号"
                
            # 如果提供了文件路径，验证文件是否存在
            file_path = kwargs.get('file_path', '')
            if file_path and not os.path.exists(file_path):
                return False, f"对比文件不存在: {file_path}"
        elif command_name == "对设备分配地址并建链":
            internal_sn = kwargs.get('internal_sn', '')
            if not internal_sn or len(internal_sn) != 17:
                return False, "RCU序列号长度必须为17位"
        elif command_name == "设置天线倾角":
            angle = kwargs.get('angle', 0)
            try:
                angle_value = int(angle)
                if angle_value < -32768 or angle_value > 32767:
                    return False, "倾角值必须在-32768到32767之间"
            except (ValueError, TypeError):
                return False, "无效的倾角值"
                
        return True, "参数验证通过"

    def process_response_with_priority(self, response: bytes) -> dict:
        """按优先级处理响应"""
        result = {
            "action": "validate",  # validate, send_response_frame, send_middle_frame, continue_next_frame
            "next_command": None,
            "validation_result": False,
            "message": "",
            "state_updated": False
        }
        
        # 第一优先级：如果正在中断模式中（等待中间帧响应）
        if self.command_state["waiting_for_middle_frame"]:
            # 检查中间帧循环次数是否超过限制
            if self.command_state["middle_frame_count"] >= self.command_state.get("max_middle_frame_attempts", 50):
                # 强制退出中断模式
                self.command_state["waiting_for_middle_frame"] = False
                self.command_state["middle_frame_count"] = 0
                
                # 检查当前响应是否还是中间帧前置，如果是则继续等待其他响应
                if self.is_special_frame(response):
                    # print(f"DEBUG - 强制退出中断模式时仍收到中间帧前置，不进行验证")
                    result["validation_result"] = False
                    result["message"] = f"强制退出中断模式时仍收到中间帧前置，等待其他响应 - 收到: {response.hex().upper()}"
                    return result
                
                # 将当前响应作为最终响应进行验证
                current_command = self.command_state["current_command"]
                if current_command:
                    params = self.command_state["command_params"].copy()
                    params["frame_num"] = self.command_state["current_frame"]
                    
                    is_valid = self.validate_response(current_command, response, **params)
                    result["validation_result"] = is_valid
                    result["message"] = f"强制退出中断模式后验证结果: {'成功' if is_valid else '失败'} - 收到: {response.hex().upper()}"
                    
                    if is_valid and self.needs_response_frame(current_command):
                        result["action"] = "send_response_frame"
                        result["next_command"] = self.get_response_frame()
                    else:
                        result["action"] = "validate"
                else:
                    result["validation_result"] = False
                    result["message"] = "强制退出中断模式，但没有当前指令"
                    
                return result
            
            # 判断是否继续中断模式
            current_command = self.command_state.get("current_command", "")
            should_continue = self.should_continue_middle_frame(current_command, response)
            
            if should_continue:
                # 继续中断模式，发送中间帧
                self.command_state["middle_frame_count"] += 1
                result["action"] = "send_middle_frame"
                result["next_command"] = self.get_middle_frame()
                result["message"] = f"继续中断模式，发送第 {self.command_state['middle_frame_count']} 个中间帧"
                result["state_updated"] = True
                return result
            else:
                # 退出中断模式，进行正常验证
                self.command_state["waiting_for_middle_frame"] = False
                self.command_state["middle_frame_count"] = 0
                
                # 检查退出时收到的是否还是中间帧前置
                if self.is_special_frame(response):
                    result["validation_result"] = False
                    result["message"] = f"退出中断模式时收到中间帧前置，等待其他响应 - 收到: {response.hex().upper()}"
                    return result
                
                # 对最后收到的响应进行验证
                if current_command:
                    params = self.command_state["command_params"].copy()
                    params["frame_num"] = self.command_state["current_frame"]
                    
                    is_valid = self.validate_response(current_command, response, **params)
                    result["validation_result"] = is_valid
                    result["message"] = f"退出中断模式后验证结果: {'成功' if is_valid else '失败'} - 收到: {response.hex().upper()}"
                    
                    if is_valid and self.needs_response_frame(current_command):
                        result["action"] = "send_response_frame"
                        result["next_command"] = self.get_response_frame()
                    else:
                        result["action"] = "validate"
                else:
                    result["validation_result"] = False
                    result["message"] = "退出中断模式，但没有当前指令"
                    
                result["state_updated"] = True
                return result
        
        # 第二优先级：检查是否收到特殊帧（中间帧前置）
        if self.is_special_frame(response):
            # 进入中断模式
            self.command_state["waiting_for_middle_frame"] = True
            self.command_state["middle_frame_count"] = 1
            result["action"] = "send_middle_frame"
            result["next_command"] = self.get_middle_frame()
            result["message"] = f"收到中间帧前置，进入中断模式，发送第 1 个中间帧 - 收到: {response.hex().upper()}"
            result["state_updated"] = True
            return result
        
        # 第三优先级：如果正在等待响应帧回传
        if self.command_state["waiting_for_response_frame"]:
            expected_echo = bytes.fromhex("7E 01 73 83 57 7E")
            if response == expected_echo:
                # 收到正确的响应帧回传
                self.command_state["waiting_for_response_frame"] = False
                self.command_state["current_frame"] += 1
                
                # 检查是否还有下一帧
                if self.command_state["current_frame"] < self.command_state["total_frames"]:
                    result["action"] = "continue_next_frame"
                    result["message"] = f"响应帧回传验证成功，准备下一帧 ({self.command_state['current_frame']}/{self.command_state['total_frames']})"
                else:
                    result["action"] = "command_complete"
                    result["message"] = "指令序列完成"
                    
                result["validation_result"] = True
                result["state_updated"] = True
                return result
            else:
                # 响应帧回传验证失败时，不要调用validate_read_combiner_response
                result["validation_result"] = False
                result["message"] = f"响应帧回传验证失败，期望: {expected_echo.hex().upper()}, 实际: {response.hex().upper()}"
                return result
        
        # 第四优先级：正常响应验证
        current_command = self.command_state.get("current_command", "")
        if current_command:
            params = self.command_state["command_params"].copy()
            params["frame_num"] = self.command_state["current_frame"]
            
            is_valid = self.validate_response(current_command, response, **params)
            result["validation_result"] = is_valid
            
            if is_valid:
                if self.needs_response_frame(current_command):
                    # 需要发送响应帧
                    self.command_state["waiting_for_response_frame"] = True
                    result["action"] = "send_response_frame"
                    result["next_command"] = self.get_response_frame()
                    result["message"] = f"验证成功，准备发送响应帧 - 收到: {response.hex().upper()}"
                else:
                    # 不需要响应帧，检查是否有下一帧
                    if self.command_state["current_frame"] < self.command_state["total_frames"]:
                        result["action"] = "continue_next_frame"
                        result["message"] = f"验证成功，准备下一帧 ({self.command_state['current_frame']}/{self.command_state['total_frames']})"
                    else:
                        result["action"] = "command_complete"
                        result["message"] = "指令完成"
            else:
                result["action"] = "validate"
                result["message"] = f"响应验证失败 - 收到: {response.hex().upper()}"
            
            result["state_updated"] = True
        else:
            result["validation_result"] = False
            result["message"] = "没有当前指令"
        
        return result

    def _generate_read_motor_speed_command(self, content: str) -> bytes:
        """生成读取电机转速指令"""
        # 根据content内容确定参数值
        param_value = 0x01 if content == '2' else 0x00  # 默认为0x00
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 04 00 48 57 90"))
        cmd_data.append(param_value)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        return bytes(cmd)

    # 新增：回读合路器信息文件相关方法
    def generate_read_combiner_info_command(self, file_type: str, frame_num: int = 0) -> bytes:
        """
        生成回读合路器信息文件指令
        
        Args:
            file_type: 文件类型('模块信息文件'/'天线信息文件'/'05'/'06')
            frame_num: 帧编号
            
        Returns:
            bytes: 生成的指令
        """
        # print(f"DEBUG - generate_read_combiner_info_command - 输入参数: file_type='{file_type}', frame_num={frame_num}")
        
        # 转换文件类型为十六进制值
        hex_file_type = self._convert_file_type_to_hex(file_type)
        file_type_value = int(hex_file_type, 16)
        
        # 构建指令内容（不包括帧头和帧尾）
        cmd_data = bytearray()
        cmd_data.extend(bytes.fromhex("01 10 90 05 00 48 57 87"))
        cmd_data.append(file_type_value)  # 05为模块文件，06为天线文件
        cmd_data.append(frame_num)
        
        # 计算CRC
        crc = self._calculate_crc(cmd_data)
        
        # 构建完整指令
        cmd = bytearray()
        cmd.append(0x7E)  # 帧头
        cmd.extend(cmd_data)
        cmd.extend(crc.to_bytes(2, byteorder='little'))
        cmd.append(0x7E)  # 帧尾
        
        # print(f"DEBUG - generate_read_combiner_info_command - 生成指令: {cmd.hex()}")
        return bytes(cmd)

    def validate_read_combiner_response(self, response: bytes, file_type: str, frame_num: int) -> bool:
        """
        验证回读合路器信息文件响应
        
        Args:
            response: 响应数据
            file_type: 期望的文件类型
            frame_num: 期望的帧编号
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if len(response) < 14:
                # print(f"DEBUG - validate_read_combiner_response - 响应长度不足: {response}")
                return False
            
            # 检查响应头部
            expected_header = bytes.fromhex("01 30 90")
            if response[1:4] != expected_header:
                # print(f"DEBUG - validate_read_combiner_response - 响应头部不匹配")
                return False
            
            # 检查固定字段
            expected_fixed = bytes.fromhex("48 57 87 00")
            if response[6:10] != expected_fixed:
                # print(f"DEBUG - validate_read_combiner_response - 固定字段不匹配")
                return False
            
            # 检查文件类型
            hex_file_type = self._convert_file_type_to_hex(file_type)
            expected_file_type = int(hex_file_type, 16)
            if response[10] != expected_file_type:
                # print(f"DEBUG - validate_read_combiner_response - 文件类型不匹配: 期望{expected_file_type:02X}, 实际{response[10]:02X}")
                return False
            
            # 检查帧编号 - 允许帧编号不匹配，但记录警告
            # if response[11] != frame_num:
                # print(f"DEBUG - validate_read_combiner_response - 帧编号不匹配(但允许继续): 期望{frame_num}, 实际{response[11]}")
                # 不返回False，允许继续处理
            
            return True
            
        except Exception as e:
            print(f"DEBUG - validate_read_combiner_response - 验证异常: {e}")
            return False

    def parse_read_combiner_response_data(self, response: bytes) -> dict:
        """
        解析回读合路器信息文件响应数据
        
        Args:
            response: 响应数据
            
        Returns:
            dict: 解析结果
        """
        result = {"valid": False}
        
        try:
            if len(response) < 14:
                return result
            
            # 解析信息域长度
            data_length = int.from_bytes(response[4:6], byteorder='little')
            
            # 解析帧编号
            frame_num = response[11] if len(response) > 11 else 0
            
            # 解析文件类型
            file_type_code = response[10] if len(response) > 10 else 0
            file_type = "模块信息文件" if file_type_code == 0x05 else "天线信息文件"
            
            # 提取数据部分（信息域长度 - 6字节固定头部）
            if data_length > 6 and len(response) >= 12 + data_length - 6:
                file_data = response[12:12 + data_length - 6]
                
                result.update({
                    "valid": True,
                    "frame_num": frame_num,
                    "file_type": file_type,
                    "file_type_code": file_type_code,
                    "data_length": data_length,
                    "file_data": file_data,
                    "data_size": len(file_data)
                })
                
                # print(f"DEBUG - parse_read_combiner_response_data - 帧{frame_num}, 类型{file_type}, 数据长度{len(file_data)}")
            
        except Exception as e:
            print(f"DEBUG - parse_read_combiner_response_data - 解析异常: {e}")
        
        return result

    def compare_frame_data_with_file(self, frame_data: bytes, file_path: str, frame_num: int, frame_size: int = 0x41) -> bool:
        """
        将单帧数据与文件对应部分进行比较
        
        Args:
            frame_data: 帧数据
            file_path: 文件路径
            frame_num: 帧编号
            frame_size: 每帧数据大小
            
        Returns:
            bool: 比较是否一致
        """
        try:
            if not os.path.exists(file_path):
                # print(f"DEBUG - compare_frame_data_with_file - 文件不存在: {file_path}")
                return False
            
            with open(file_path, 'rb') as f:
                # 定位到对应帧的起始位置
                start_pos = frame_num * frame_size
                f.seek(start_pos)
                
                # 读取对应长度的数据
                expected_data = f.read(len(frame_data))
                
                # 比较数据
                is_match = frame_data == expected_data
                
                # print(f"DEBUG - compare_frame_data_with_file - 帧{frame_num}比较结果: {'一致' if is_match else '不一致'}")
                # print(f"DEBUG - 文件路径: {file_path}")
                # print(f"DEBUG - 起始位置: {start_pos}")
                # print(f"DEBUG - 实际数据长度: {len(frame_data)}, 期望数据长度: {len(expected_data)}")
                
                if not is_match:
                    # print(f"DEBUG - 实际数据: {frame_data.hex().upper()}")
                    # print(f"DEBUG - 期望数据: {expected_data.hex().upper()}")
                    # 找出第一个不匹配的位置
                    min_len = min(len(frame_data), len(expected_data))
                    for i in range(min_len):
                        if frame_data[i] != expected_data[i]:
                            # print(f"DEBUG - 第一个不匹配位置: {i}, 实际值: {frame_data[i]:02X}, 期望值: {expected_data[i]:02X}")
                            break
                
                return is_match
                
        except Exception as e:
            print(f"DEBUG - compare_frame_data_with_file - 比较异常: {e}")
            return False

    def generate_read_combiner_info_file_commands(self, file_type: str, total_frames: int = None) -> List[bytes]:
        """
        生成回读合路器信息文件的多帧指令
        
        Args:
            file_type: 文件类型
            total_frames: 总帧数（如果为None则使用默认值）
            
        Returns:
            List[bytes]: 指令列表
        """
        commands = []
        max_frames = total_frames or 100  # 默认最大帧数

        for frame_num in range(max_frames):
            cmd = self.generate_read_combiner_info_command(file_type, frame_num)
            commands.append(cmd)

        return commands


class PowerCommandHandler:
    """电源指令处理类，专门处理ASCII格式的电源控制指令"""

    def __init__(self, logger_manager=None, parent_handler=None):
        self.logger_manager = logger_manager
        self.parent_handler = parent_handler  # 引用主CommandHandler

        # 电源指令集定义
        self.power_commands = {
            "查询上位机状态": ["REMOTE", "OUT0", "VSET1:12", "VSET2:12", "ISET1:2", "ISET2:2", "*IDN?"],
            "切换供电开关": ["OUT1"],
            "查询供电开关": ["STATUS?"],
            "读取电流值": "IOUT{port}?",  # {port}由用户输入决定
            "读取电压值": "VOUT{port}?"   # {port}由用户输入决定
        }

        # 校准状态管理
        self.calibration_state = {
            "is_calibrating": False,
            "calibration_start_time": None,
            "calibration_duration": 30.0,  # 30秒校准时间
            "read_count": 0,
            "max_reads": 100,
            "voltage_readings": [],
            "current_readings": [],
            "alternating_mode": False,  # 是否交替执行电压电流读取
            "current_read_type": "voltage"  # "voltage" 或 "current"
        }

        # 换行符定义
        self.line_ending = b'\r\n'  # 0x0D 0x0A

    def generate_power_command(self, command_name: str, **kwargs) -> Optional[bytes]:
        """
        生成电源指令
        :param command_name: 指令名称
        :param kwargs: 指令参数，如port等
        :return: 生成的ASCII指令字节串
        """
        try:
            if command_name == "查询上位机状态":
                return self._generate_status_query_sequence()
            elif command_name == "切换供电开关":
                return self._generate_switch_power_command()
            elif command_name == "查询供电开关":
                return self._generate_query_power_status_command()
            elif command_name == "读取电流值":
                port = kwargs.get('port', '1')
                return self._generate_read_current_command(port)
            elif command_name == "读取电压值":
                port = kwargs.get('port', '1')
                return self._generate_read_voltage_command(port)
            else:
                self._log_error(f"未知的电源指令: {command_name}")
                return None
        except Exception as e:
            self._log_error(f"生成电源指令失败: {e}")
            return None

    def generate_power_command_list(self, command_name: str, **kwargs) -> List[bytes]:
        """
        生成电源指令列表（用于间隔发送）
        :param command_name: 指令名称
        :param kwargs: 指令参数，如port等
        :return: 生成的ASCII指令字节串列表
        """
        try:
            if command_name == "查询上位机状态":
                return self.generate_status_query_commands_separately()
            elif command_name == "切换供电开关":
                return [self._generate_switch_power_command()]
            elif command_name == "查询供电开关":
                return [self._generate_query_power_status_command()]
            elif command_name == "读取电流值":
                port = kwargs.get('port', '1')
                return [self._generate_read_current_command(port)]
            elif command_name == "读取电压值":
                port = kwargs.get('port', '1')
                return [self._generate_read_voltage_command(port)]
            else:
                self._log_error(f"未知的电源指令: {command_name}")
                return []
        except Exception as e:
            self._log_error(f"生成电源指令列表失败: {e}")
            return []

    def _generate_status_query_sequence(self) -> bytes:
        """生成查询上位机状态指令序列"""
        commands = self.power_commands["查询上位机状态"]
        command_bytes = bytearray()

        for cmd in commands:
            command_bytes.extend(cmd.encode('ascii'))
            command_bytes.extend(self.line_ending)

        return bytes(command_bytes)

    def generate_status_query_commands_separately(self) -> List[bytes]:
        """
        分别生成查询上位机状态的各个指令
        用于需要逐个发送并等待响应的场景
        """
        commands = self.power_commands["查询上位机状态"]
        command_list = []

        for cmd in commands:
            command_bytes = bytearray()
            command_bytes.extend(cmd.encode('ascii'))
            command_bytes.extend(self.line_ending)
            command_list.append(bytes(command_bytes))

        return command_list

    def is_final_status_query_command(self, command_bytes: bytes) -> bool:
        """检查是否是状态查询序列的最后一个指令(*IDN?)"""
        try:
            command_str = command_bytes.replace(self.line_ending, b'').decode('ascii')
            return command_str == "*IDN?"
        except:
            return False

    def validate_device_info_response(self, response: bytes) -> bool:
        """验证设备信息响应是否有效"""
        try:
            if not response:
                return False

            # 移除换行符并转换为字符串
            response_str = response.replace(self.line_ending, b'').decode('ascii', errors='ignore')

            # 设备信息响应应该包含一些基本信息
            # 这里可以根据实际设备的响应格式进行调整
            if len(response_str) > 0 and response_str.strip():
                self._log_info(f"收到设备信息: {response_str}")
                return True

            return False
        except Exception as e:
            self._log_error(f"验证设备信息响应失败: {e}")
            return False

    def _generate_switch_power_command(self) -> bytes:
        """生成切换供电开关指令"""
        cmd = self.power_commands["切换供电开关"][0]
        command_bytes = bytearray()
        command_bytes.extend(cmd.encode('ascii'))
        command_bytes.extend(self.line_ending)
        return bytes(command_bytes)

    def _generate_query_power_status_command(self) -> bytes:
        """生成查询供电开关指令"""
        cmd = self.power_commands["查询供电开关"][0]
        command_bytes = bytearray()
        command_bytes.extend(cmd.encode('ascii'))
        command_bytes.extend(self.line_ending)
        return bytes(command_bytes)

    def _generate_read_current_command(self, port: str) -> bytes:
        """生成读取电流值指令"""
        cmd_template = self.power_commands["读取电流值"]
        cmd = cmd_template.format(port=port)
        command_bytes = bytearray()
        command_bytes.extend(cmd.encode('ascii'))
        command_bytes.extend(self.line_ending)
        return bytes(command_bytes)

    def _generate_read_voltage_command(self, port: str) -> bytes:
        """生成读取电压值指令"""
        cmd_template = self.power_commands["读取电压值"]
        cmd = cmd_template.format(port=port)
        command_bytes = bytearray()
        command_bytes.extend(cmd.encode('ascii'))
        command_bytes.extend(self.line_ending)
        return bytes(command_bytes)

    def parse_power_response(self, response: bytes, command_name: str) -> Dict:
        """
        解析电源指令响应
        :param response: 接收到的响应数据
        :param command_name: 对应的指令名称
        :return: 解析结果字典
        """
        try:
            # 移除换行符并转换为字符串
            response_str = response.replace(self.line_ending, b'').decode('ascii', errors='ignore')

            result = {
                "success": True,
                "raw_response": response_str,
                "parsed_data": None,
                "message": ""
            }

            if command_name == "查询上位机状态":
                result["parsed_data"] = self._parse_status_query_response(response_str)
            elif command_name == "查询供电开关":
                result["parsed_data"] = self._parse_power_status_response(response_str)
            elif command_name == "读取电流值":
                result["parsed_data"] = self._parse_current_response(response_str)
            elif command_name == "读取电压值":
                result["parsed_data"] = self._parse_voltage_response(response_str)
            elif command_name == "切换供电开关":
                result["message"] = "供电开关切换指令已发送"

            return result
        except Exception as e:
            return {
                "success": False,
                "raw_response": response.hex() if isinstance(response, bytes) else str(response),
                "parsed_data": None,
                "message": f"解析响应失败: {e}"
            }

    def _parse_status_query_response(self, response: str) -> Dict:
        """解析查询上位机状态响应（主要是*IDN?的设备信息）"""
        return {
            "device_info": response,
            "status": "设备信息获取成功" if response else "设备信息获取失败"
        }

    def _parse_power_status_response(self, response: str) -> Dict:
        """解析查询供电开关响应（8位二进制数据）"""
        try:
            # 假设响应是8位二进制字符串或十六进制
            if len(response) == 8 and all(c in '01' for c in response):
                # 直接是二进制字符串
                binary_data = response
            else:
                # 尝试解析为十六进制然后转换为二进制
                hex_value = int(response, 16)
                binary_data = format(hex_value, '08b')

            return {
                "binary_data": binary_data,
                "channel_mode": {
                    "ch1": "恒压模式" if binary_data[7] == '1' else "恒流模式",  # 第0位
                    "ch2": "恒压模式" if binary_data[6] == '1' else "恒流模式"   # 第1位
                },
                "power_mode": self._decode_power_mode(binary_data[5:3:-1]),  # 第2-3位
                "buzzer": "开" if binary_data[3] == '1' else "关",  # 第4位
                "output": "开" if binary_data[2] == '1' else "关",  # 第5位
                "baud_rate": self._decode_baud_rate(binary_data[1::-1])  # 第6-7位
            }
        except Exception as e:
            return {"error": f"解析供电状态失败: {e}", "raw_data": response}

    def _decode_power_mode(self, mode_bits: str) -> str:
        """解码电源模式"""
        mode_map = {
            "00": "独立",
            "01": "独立",
            "10": "并联",
            "11": "串联"
        }
        return mode_map.get(mode_bits, "未知模式")

    def _decode_baud_rate(self, baud_bits: str) -> str:
        """解码波特率"""
        baud_map = {
            "00": "115200bps",
            "01": "57600bps",
            "10": "9600bps",
            "11": "未知波特率"
        }
        return baud_map.get(baud_bits, "未知波特率")

    def _parse_current_response(self, response: str) -> Dict:
        """解析电流值响应（x.xxxA格式）"""
        try:
            if response.endswith('A'):
                current_value = float(response[:-1])
                return {
                    "current": current_value,
                    "unit": "A",
                    "formatted": response
                }
            else:
                return {"error": "电流值格式不正确", "raw_data": response}
        except Exception as e:
            return {"error": f"解析电流值失败: {e}", "raw_data": response}

    def _parse_voltage_response(self, response: str) -> Dict:
        """解析电压值响应（xx.xxxV或x.xxxV格式）"""
        try:
            if response.endswith('V'):
                voltage_value = float(response[:-1])
                return {
                    "voltage": voltage_value,
                    "unit": "V",
                    "formatted": response
                }
            else:
                return {"error": "电压值格式不正确", "raw_data": response}
        except Exception as e:
            return {"error": f"解析电压值失败: {e}", "raw_data": response}

    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logger_manager:
            self.logger_manager.log_info(message, "power")
        else:
            print(f"[POWER INFO] {message}")

    def _log_error(self, message: str):
        """记录错误日志"""
        if self.logger_manager:
            self.logger_manager.log_error(message, "power")
        else:
            print(f"[POWER ERROR] {message}")

    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.logger_manager:
            self.logger_manager.log_debug(message, "power")
        else:
            print(f"[POWER DEBUG] {message}")

    def start_calibration(self):
        """开始校准过程"""
        self.calibration_state["is_calibrating"] = True
        self.calibration_state["calibration_start_time"] = time.time()
        self.calibration_state["read_count"] = 0
        self.calibration_state["voltage_readings"] = []
        self.calibration_state["current_readings"] = []
        self.calibration_state["current_read_type"] = "voltage"
        self._log_info("开始校准过程，30秒内将进行特殊读取")

    def stop_calibration(self):
        """停止校准过程"""
        self.calibration_state["is_calibrating"] = False
        self.calibration_state["calibration_start_time"] = None
        self._log_info("校准过程结束")

    def is_in_calibration(self) -> bool:
        """检查是否在校准期间"""
        if not self.calibration_state["is_calibrating"]:
            return False

        if self.calibration_state["calibration_start_time"] is None:
            return False

        elapsed_time = time.time() - self.calibration_state["calibration_start_time"]
        return elapsed_time < self.calibration_state["calibration_duration"]

    def should_use_calibration_reading(self, command_name: str) -> bool:
        """判断是否应该使用校准期间的特殊读取逻辑"""
        return (self.is_in_calibration() and
                command_name in ["读取电压值", "读取电流值"])

    def execute_calibration_reading(self, command_name: str, port: str, serial_manager, motor_serial_manager=None) -> Dict:
        """
        执行校准期间的特殊读取逻辑
        在校准时间内读取100次，取最大值
        如果电压电流两个指令同时出现，则交替执行各100次
        使用非阻塞方式避免sleep影响校准过程
        支持并行处理电机转速读取（使用不同串口）
        """
        try:
            if not self.should_use_calibration_reading(command_name):
                return {"success": False, "message": "不在校准期间或指令类型不匹配"}

            # 检查是否需要交替执行
            if self.calibration_state["alternating_mode"]:
                return self._execute_alternating_reading_non_blocking(command_name, port, serial_manager, motor_serial_manager)
            else:
                return self._execute_single_type_reading_non_blocking(command_name, port, serial_manager, motor_serial_manager)

        except Exception as e:
            self._log_error(f"校准期间读取失败: {e}")
            return {"success": False, "message": f"校准读取异常: {e}"}

    def _execute_single_type_reading(self, command_name: str, port: str, serial_manager) -> Dict:
        """执行单一类型的校准读取（100次）"""
        readings = []
        max_reads = self.calibration_state["max_reads"]

        self._log_info(f"开始校准期间{command_name}读取，共{max_reads}次")

        for i in range(max_reads):
            if not self.is_in_calibration():
                self._log_info(f"校准时间结束，已读取{i}次")
                break

            # 生成指令
            if command_name == "读取电压值":
                cmd_bytes = self._generate_read_voltage_command(port)
            else:
                cmd_bytes = self._generate_read_current_command(port)

            # 发送指令并接收响应
            if serial_manager.send_command(cmd_bytes, f"校准{command_name}_{i+1}"):
                response = serial_manager.receive_single_frame(timeout=1.0)
                if response:
                    parsed = self.parse_power_response(response, command_name)
                    if parsed["success"] and parsed["parsed_data"]:
                        if command_name == "读取电压值":
                            value = parsed["parsed_data"].get("voltage", 0)
                        else:
                            value = parsed["parsed_data"].get("current", 0)
                        readings.append(value)
                        self._log_debug(f"第{i+1}次读取: {value}")

            # 使用最小延时，避免影响校准过程
            # time.sleep(0.01)  # 注释掉sleep，改用非阻塞方式

        # 计算最大值
        if readings:
            max_value = max(readings)
            avg_value = sum(readings) / len(readings)
            result = {
                "success": True,
                "max_value": max_value,
                "average_value": avg_value,
                "total_readings": len(readings),
                "all_readings": readings,
                "command_type": command_name
            }
            self._log_info(f"校准{command_name}完成，读取{len(readings)}次，最大值: {max_value}")
            return result
        else:
            return {"success": False, "message": "校准期间未获取到有效读数"}

    def _execute_single_type_reading_non_blocking(self, command_name: str, port: str, serial_manager, motor_serial_manager=None) -> Dict:
        """执行单一类型的校准读取（100次）- 非阻塞版本，支持并行电机转速读取"""
        readings = []
        motor_readings = []
        max_reads = self.calibration_state["max_reads"]

        self._log_info(f"开始校准期间{command_name}读取，共{max_reads}次（非阻塞模式）")
        if motor_serial_manager:
            self._log_info("同时启动并行电机转速读取")

        # 使用队列来管理读取任务，避免阻塞
        read_count = 0
        successful_reads = 0
        motor_successful_reads = 0

        while read_count < max_reads and self.is_in_calibration():
            read_count += 1

            # 生成电源指令
            if command_name == "读取电压值":
                cmd_bytes = self._generate_read_voltage_command(port)
            else:
                cmd_bytes = self._generate_read_current_command(port)

            # 并行处理：同时发送电源指令和电机转速指令
            power_sent = serial_manager.send_command(cmd_bytes, f"校准{command_name}_{read_count}", is_response=False)
            motor_sent = False

            # 如果有电机串口管理器，并行发送电机转速读取指令
            if motor_serial_manager and self.parent_handler:
                motor_cmd = self.parent_handler.generate_command("读取电机转速")
                if motor_cmd:
                    motor_sent = motor_serial_manager.send_command(motor_cmd, f"校准电机转速_{read_count}")

            # 并行接收响应
            if power_sent:
                # 使用较短的超时时间，避免长时间等待
                response = serial_manager.receive_single_frame(timeout=0.1)
                if response:
                    parsed = self.parse_power_response(response, command_name)
                    if parsed["success"] and parsed["parsed_data"]:
                        if command_name == "读取电压值":
                            value = parsed["parsed_data"].get("voltage", 0)
                        else:
                            value = parsed["parsed_data"].get("current", 0)
                        readings.append(value)
                        successful_reads += 1
                        self._log_debug(f"第{read_count}次{command_name}读取成功: {value}")
                    else:
                        self._log_debug(f"第{read_count}次{command_name}读取解析失败")
                else:
                    self._log_debug(f"第{read_count}次{command_name}读取无响应")
            else:
                self._log_debug(f"第{read_count}次{command_name}指令发送失败")

            # 并行接收电机转速响应
            if motor_sent and motor_serial_manager:
                motor_response = motor_serial_manager.receive_single_frame(timeout=0.1)
                if motor_response:
                    # 这里可以解析电机转速响应
                    motor_successful_reads += 1
                    motor_readings.append(motor_response)
                    self._log_debug(f"第{read_count}次电机转速读取成功")

        # 计算结果
        result = {
            "success": True if readings else False,
            "command_type": command_name,
            "total_attempts": read_count,
            "successful_reads": successful_reads
        }

        if readings:
            max_value = max(readings)
            avg_value = sum(readings) / len(readings)
            result.update({
                "max_value": max_value,
                "average_value": avg_value,
                "total_readings": len(readings),
                "all_readings": readings
            })
            self._log_info(f"校准{command_name}完成，尝试{read_count}次，成功{successful_reads}次，最大值: {max_value}")
        else:
            result["message"] = f"校准期间未获取到有效读数，尝试{read_count}次"

        # 添加电机转速读取结果
        if motor_readings:
            result["motor_readings"] = {
                "total_readings": len(motor_readings),
                "successful_reads": motor_successful_reads,
                "all_responses": motor_readings
            }
            self._log_info(f"并行电机转速读取完成，成功{motor_successful_reads}次")

        return result

    def _execute_alternating_reading(self, command_name: str, port: str, serial_manager) -> Dict:
        """执行交替读取（电压电流各100次）"""
        voltage_readings = []
        current_readings = []
        max_reads_per_type = self.calibration_state["max_reads"]

        self._log_info(f"开始校准期间交替读取，电压电流各{max_reads_per_type}次")

        voltage_count = 0
        current_count = 0

        while (voltage_count < max_reads_per_type or current_count < max_reads_per_type) and self.is_in_calibration():
            # 决定当前读取类型
            if voltage_count < max_reads_per_type and current_count < max_reads_per_type:
                # 两种都还没读完，交替进行
                current_type = "voltage" if self.calibration_state["current_read_type"] == "voltage" else "current"
                # 切换下次读取类型
                self.calibration_state["current_read_type"] = "current" if current_type == "voltage" else "voltage"
            elif voltage_count < max_reads_per_type:
                current_type = "voltage"
            else:
                current_type = "current"

            # 执行读取
            if current_type == "voltage" and voltage_count < max_reads_per_type:
                cmd_bytes = self._generate_read_voltage_command(port)
                cmd_name = "读取电压值"
                voltage_count += 1
                read_index = voltage_count
            elif current_type == "current" and current_count < max_reads_per_type:
                cmd_bytes = self._generate_read_current_command(port)
                cmd_name = "读取电流值"
                current_count += 1
                read_index = current_count
            else:
                continue

            # 发送指令并接收响应
            if serial_manager.send_command(cmd_bytes, f"校准交替{cmd_name}_{read_index}"):
                response = serial_manager.receive_single_frame(timeout=1.0)
                if response:
                    parsed = self.parse_power_response(response, cmd_name)
                    if parsed["success"] and parsed["parsed_data"]:
                        if current_type == "voltage":
                            value = parsed["parsed_data"].get("voltage", 0)
                            voltage_readings.append(value)
                            self._log_debug(f"电压第{voltage_count}次读取: {value}V")
                        else:
                            value = parsed["parsed_data"].get("current", 0)
                            current_readings.append(value)
                            self._log_debug(f"电流第{current_count}次读取: {value}A")

            # 移除sleep，使用非阻塞方式
            # time.sleep(0.01)

        # 计算结果
        result = {"success": True, "alternating_mode": True}

        if voltage_readings:
            result["voltage"] = {
                "max_value": max(voltage_readings),
                "average_value": sum(voltage_readings) / len(voltage_readings),
                "total_readings": len(voltage_readings),
                "all_readings": voltage_readings
            }

        if current_readings:
            result["current"] = {
                "max_value": max(current_readings),
                "average_value": sum(current_readings) / len(current_readings),
                "total_readings": len(current_readings),
                "all_readings": current_readings
            }

        self._log_info(f"校准交替读取完成，电压{len(voltage_readings)}次，电流{len(current_readings)}次")
        return result

    def _execute_alternating_reading_non_blocking(self, command_name: str, port: str, serial_manager, motor_serial_manager=None) -> Dict:
        """执行交替读取（电压电流各100次）- 非阻塞版本"""
        voltage_readings = []
        current_readings = []
        max_reads_per_type = self.calibration_state["max_reads"]

        self._log_info(f"开始校准期间交替读取，电压电流各{max_reads_per_type}次（非阻塞模式）")

        voltage_count = 0
        current_count = 0
        voltage_success = 0
        current_success = 0

        while (voltage_count < max_reads_per_type or current_count < max_reads_per_type) and self.is_in_calibration():
            # 决定当前读取类型
            if voltage_count < max_reads_per_type and current_count < max_reads_per_type:
                # 两种都还没读完，交替进行
                current_type = "voltage" if self.calibration_state["current_read_type"] == "voltage" else "current"
                # 切换下次读取类型
                self.calibration_state["current_read_type"] = "current" if current_type == "voltage" else "voltage"
            elif voltage_count < max_reads_per_type:
                current_type = "voltage"
            else:
                current_type = "current"

            # 执行读取
            if current_type == "voltage" and voltage_count < max_reads_per_type:
                cmd_bytes = self._generate_read_voltage_command(port)
                cmd_name = "读取电压值"
                voltage_count += 1
                read_index = voltage_count
            elif current_type == "current" and current_count < max_reads_per_type:
                cmd_bytes = self._generate_read_current_command(port)
                cmd_name = "读取电流值"
                current_count += 1
                read_index = current_count
            else:
                continue

            # 发送指令并接收响应（非阻塞）
            if serial_manager.send_command(cmd_bytes, f"校准交替{cmd_name}_{read_index}", is_response=False):
                response = serial_manager.receive_single_frame(timeout=0.1)
                if response:
                    parsed = self.parse_power_response(response, cmd_name)
                    if parsed["success"] and parsed["parsed_data"]:
                        if current_type == "voltage":
                            value = parsed["parsed_data"].get("voltage", 0)
                            voltage_readings.append(value)
                            voltage_success += 1
                            self._log_debug(f"电压第{voltage_count}次读取成功: {value}V")
                        else:
                            value = parsed["parsed_data"].get("current", 0)
                            current_readings.append(value)
                            current_success += 1
                            self._log_debug(f"电流第{current_count}次读取成功: {value}A")

        # 计算结果
        result = {
            "success": True,
            "alternating_mode": True,
            "total_attempts": {"voltage": voltage_count, "current": current_count},
            "successful_reads": {"voltage": voltage_success, "current": current_success}
        }

        if voltage_readings:
            result["voltage"] = {
                "max_value": max(voltage_readings),
                "average_value": sum(voltage_readings) / len(voltage_readings),
                "total_readings": len(voltage_readings),
                "all_readings": voltage_readings
            }

        if current_readings:
            result["current"] = {
                "max_value": max(current_readings),
                "average_value": sum(current_readings) / len(current_readings),
                "total_readings": len(current_readings),
                "all_readings": current_readings
            }

        self._log_info(f"校准交替读取完成，电压尝试{voltage_count}次成功{voltage_success}次，电流尝试{current_count}次成功{current_success}次")
        return result

    def set_alternating_mode(self, enable: bool):
        """设置是否启用交替读取模式"""
        self.calibration_state["alternating_mode"] = enable
        mode_str = "启用" if enable else "禁用"
        self._log_info(f"{mode_str}交替读取模式")

    def get_power_command_list(self) -> Dict[str, str]:
        """获取所有电源指令列表"""
        return {
            "查询上位机状态": "电源指令集(包含REMOTE,OUT0,VSET1:12,VSET2:12,ISET1:2,ISET2:2,*IDN?)",
            "切换供电开关": "电源指令(OUT1)",
            "查询供电开关": "电源指令(STATUS?,返回8位二进制数据)",
            "读取电流值": "电源指令(IOUT<X>?,需要port参数)",
            "读取电压值": "电源指令(VOUT<X>?,需要port参数)"
        }


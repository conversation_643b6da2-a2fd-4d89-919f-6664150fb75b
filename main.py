# -*- coding: utf-8 -*-
"""
上位机自动化测试系统主程序
使用模块化架构，支持串口通信、指令处理、测试流程控制等功能
"""

import sys
import os
from PyQt5 import QtWidgets, QtCore
from ui.Main_Window import Ui_MainWindow
from function.app_controller import AppController

# 定义一个简单的函数来处理QVector<int>警告
# 这个警告通常不会影响程序功能，可以安全忽略
def register_qt_types():
    """尝试注册Qt类型以避免警告，如果失败则忽略"""
    try:
        # 尝试多种方法注册Qt类型
        from PyQt5.QtCore import qRegisterMetaType
        qRegisterMetaType("QVector<int>")
        qRegisterMetaType("QList<int>")
        return True
    except ImportError:
        # 如果无法导入qRegisterMetaType，尝试其他方法
        try:
            from PyQt5.QtCore import QMetaType
            # 在某些PyQt5版本中，可能需要使用不同的方法
            if hasattr(QMetaType, 'type'):
                QMetaType.type("QVector<int>")
                QMetaType.type("QList<int>")
                return True
        except:
            pass
    except Exception:
        # 如果注册失败，也不影响程序运行
        pass
    return False

def main():
    """主函数"""
    try:
        # 创建QApplication实例
        app = QtWidgets.QApplication(sys.argv)

        # 尝试注册自定义类型以避免QVector<int>警告
        if register_qt_types():
            print("成功注册Qt元类型")
        else:
            print("注意: 无法注册Qt元类型，可能会出现QVector<int>警告，但不影响程序功能")

        # 设置应用程序属性
        app.setApplicationName("上位机自动化测试系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("自动化测试系统")

        # 确保使用UTF-8编码
        QtCore.QTextCodec.setCodecForLocale(QtCore.QTextCodec.codecForName("UTF-8"))
        
        # 创建主窗口
        main_window = QtWidgets.QMainWindow()
        ui = Ui_MainWindow()
        ui.setupUi(main_window)
        
        # 初始化应用控制器
        controller = AppController(ui)
        controller.setup_command_interaction()  # 兼容性调用
        
        # 显示主窗口
        main_window.show()
        
        # 启动事件循环
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"启动应用程序失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
